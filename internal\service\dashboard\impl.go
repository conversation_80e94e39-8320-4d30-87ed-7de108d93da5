package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	dashboardRepo "github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	financialsheetService "github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// isNotFoundError checks if the error is a NotFound error
func isNotFoundError(err error) bool {
	if domainErr, ok := err.(*errors.DomainError); ok {
		return domainErr.Kind() == errors.NotFound
	}
	return false
}

type service struct {
	Repository            dashboardRepo.Repository
	FinancialSheetService financialsheetService.Service
}

// New creates a new dashboard service
func New(repository dashboardRepo.Repository, financialSheetService financialsheetService.Service) Service {
	return &service{
		Repository:            repository,
		FinancialSheetService: financialSheetService,
	}
}

// FindFinancialMap implements the unified approach for dashboard data
func (s *service) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Try to fetch from unified collection
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// If found, update with live income data and return
	if financialMap != nil {
		return s.updateFinancialMapWithLiveData(ctx, financialMap)
	}

	// If not found, create a new financial map with live data
	return s.createNewFinancialMap(ctx, userID)
}

// updateFinancialMapWithLiveData updates the financial map with live income data from financial sheet
func (s *service) updateFinancialMapWithLiveData(ctx context.Context, financialMap *dashboard.FinancialMap) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	liveIncomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, financialMap.UserID)
	if err != nil {
		return nil, err
	}

	// Calculate monthly income from live data
	var monthlyIncome monetary.Amount
	incomeSourcesSlice := make([]dashboard.IncomeSource, 0, len(liveIncomeSources))
	for _, source := range liveIncomeSources {
		incomeSourcesSlice = append(incomeSourcesSlice, *source)
		monthlyIncome += source.MonthlyAmount
	}

	// Update financial map with live income data
	financialMap.IncomeSources = incomeSourcesSlice
	financialMap.MonthlyIncome = monthlyIncome

	// Create dynamic snapshot for current month
	var emergencyFundValue monetary.Amount
	if financialMap.EmergencyFund != nil {
		emergencyFundValue = financialMap.EmergencyFund.CurrentValue
	}

	currentSnapshot := dashboard.NetWorthSnapshot{
		UserID:             financialMap.UserID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         emergencyFundValue + financialMap.TotalInvestments + financialMap.TotalAssets,
	}

	// Add current snapshot to history (limit to 12 months total)
	financialMap.NetWorthHistory = append(financialMap.NetWorthHistory, currentSnapshot)
	if len(financialMap.NetWorthHistory) > 12 {
		financialMap.NetWorthHistory = financialMap.NetWorthHistory[len(financialMap.NetWorthHistory)-12:]
	}

	return financialMap, nil
}

// createNewFinancialMap creates a new financial map with live data
func (s *service) createNewFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Calculate monthly income
	var monthlyIncome monetary.Amount
	incomeSourcesSlice := make([]dashboard.IncomeSource, 0, len(incomeSources))
	for _, source := range incomeSources {
		incomeSourcesSlice = append(incomeSourcesSlice, *source)
		monthlyIncome += source.MonthlyAmount
	}

	// Create new financial map
	financialMap := &dashboard.FinancialMap{
		UserID:           userID,
		MonthlyIncome:    monthlyIncome,
		EmergencyFund:    nil, // Will be nil until user sets it
		TotalInvestments: 0,
		TotalAssets:      0,
		NetWorthHistory:  []dashboard.NetWorthSnapshot{},
		IncomeSources:    incomeSourcesSlice,
		Investments:      []dashboard.Investment{},
		Assets:           []dashboard.Asset{},
	}

	// Prepare for creation
	if err := financialMap.PrepareCreate(); err != nil {
		return nil, err
	}

	// Save to database
	if err := s.Repository.SaveFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return financialMap, nil
}

// aggregateIncomeSourcesFromTransactions fetches income data from financial sheet transactions
func (s *service) aggregateIncomeSourcesFromTransactions(ctx context.Context, userID string) (map[string]*dashboard.IncomeSource, error) {
	// Get current month and year
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Fetch income transactions from financial sheet
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeIncome, currentYear, currentMonth)
	if err != nil {
		return nil, err
	}

	// Aggregate income by money source
	incomeMap := make(map[string]*dashboard.IncomeSource)
	for _, transaction := range transactions {
		key := string(rune(transaction.MoneySource))
		if incomeSource, exists := incomeMap[key]; exists {
			incomeSource.MonthlyAmount += transaction.Value
		} else {
			// Get category info to determine the name
			categoryName := "Income Source" // Default name
			if transaction.Category != "" {
				categoryName = string(transaction.Category)
			}

			incomeMap[key] = &dashboard.IncomeSource{
				UserID:        userID,
				Name:          categoryName,
				MonthlyAmount: transaction.Value,
				MoneySource:   transaction.MoneySource,
			}
			// Prepare for creation to set timestamps
			incomeMap[key].PrepareCreate()
		}
	}

	return incomeMap, nil
}

// Specialized CRUD operations for individual components within the unified collection

// CreateIncomeSource creates a new income source within the unified financial map
func (s *service) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount, moneySource financialsheet.MoneySource) (*dashboard.IncomeSource, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new income source
	incomeSource := &dashboard.IncomeSource{
		UserID:        userID,
		Name:          name,
		MonthlyAmount: monthlyAmount,
		MoneySource:   moneySource,
	}

	if err := incomeSource.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.IncomeSources = append(financialMap.IncomeSources, *incomeSource)

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return incomeSource, nil
}

// UpdateEmergencyFund updates the emergency fund within the unified financial map
func (s *service) UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return err
		}
	}

	// Update or create emergency fund
	if financialMap.EmergencyFund == nil {
		financialMap.EmergencyFund = &dashboard.EmergencyFund{
			UserID:       userID,
			CurrentValue: currentValue,
			GoalValue:    0, // Default goal value
		}
		if err := financialMap.EmergencyFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.EmergencyFund.CurrentValue = currentValue
		if err := financialMap.EmergencyFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// FindIncomeSources returns income sources from the unified financial map
func (s *service) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.IncomeSource{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.IncomeSource, len(financialMap.IncomeSources))
	for i := range financialMap.IncomeSources {
		result[i] = &financialMap.IncomeSources[i]
	}

	return result, nil
}

// UpdateIncomeSource updates an income source within the unified financial map
func (s *service) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	incomeSourceID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	// Find the financial map that contains this income source
	// Note: This is inefficient as it requires searching across all financial maps
	// The proper solution would be to update the controller to extract userID and pass it here
	financialMap, err := s.findFinancialMapByIncomeSourceID(ctx, incomeSourceID)
	if err != nil {
		return err
	}

	// Find and update the specific income source
	found := false
	for i := range financialMap.IncomeSources {
		if financialMap.IncomeSources[i].ObjectID == incomeSourceID {
			financialMap.IncomeSources[i].Name = name
			financialMap.IncomeSources[i].MonthlyAmount = monthlyAmount

			// Prepare for update
			if err := financialMap.IncomeSources[i].PrepareUpdate(); err != nil {
				return err
			}
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "income source not found", errors.NotFound, nil)
	}

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// DeleteIncomeSource deletes an income source from the unified financial map
func (s *service) DeleteIncomeSource(ctx context.Context, id string) error {
	incomeSourceID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	// Find the financial map that contains this income source
	// Note: This is inefficient as it requires searching across all financial maps
	// The proper solution would be to update the controller to extract userID and pass it here
	financialMap, err := s.findFinancialMapByIncomeSourceID(ctx, incomeSourceID)
	if err != nil {
		return err
	}

	// Find and remove the specific income source
	found := false
	for i := range financialMap.IncomeSources {
		if financialMap.IncomeSources[i].ObjectID == incomeSourceID {
			// Remove the income source from the slice
			financialMap.IncomeSources = append(financialMap.IncomeSources[:i], financialMap.IncomeSources[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "income source not found", errors.NotFound, nil)
	}

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// findFinancialMapByIncomeSourceID finds the financial map that contains the specified income source
// Note: This is an inefficient workaround. The proper solution would be to pass userID from the controller.
func (s *service) findFinancialMapByIncomeSourceID(ctx context.Context, incomeSourceID primitive.ObjectID) (*dashboard.FinancialMap, error) {
	// Use the repository's FindIncomeSource method which searches within financial maps
	incomeSource, err := s.Repository.FindIncomeSource(ctx, incomeSourceID)
	if err != nil {
		return nil, err
	}

	// Now find the financial map for this user
	return s.Repository.FindFinancialMap(ctx, incomeSource.UserID)
}

// FindEmergencyFund returns the emergency fund from the unified financial map
func (s *service) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
		}
		return nil, err
	}

	if financialMap.EmergencyFund == nil {
		return nil, errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
	}

	return financialMap.EmergencyFund, nil
}

// UpdateEmergencyFundGoal updates the emergency fund goal within the unified financial map
func (s *service) UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return err
		}
	}

	// Update or create emergency fund
	if financialMap.EmergencyFund == nil {
		financialMap.EmergencyFund = &dashboard.EmergencyFund{
			UserID:       userID,
			CurrentValue: 0, // Default current value
			GoalValue:    goalValue,
		}
		if err := financialMap.EmergencyFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.EmergencyFund.GoalValue = goalValue
		if err := financialMap.EmergencyFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// Investment operations
func (s *service) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new investment
	investment := &dashboard.Investment{
		UserID:       userID,
		Name:         name,
		CurrentValue: currentValue,
	}

	if err := investment.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.Investments = append(financialMap.Investments, *investment)

	// Recalculate total investments
	var totalInvestments monetary.Amount
	for _, inv := range financialMap.Investments {
		totalInvestments += inv.CurrentValue
	}
	financialMap.TotalInvestments = totalInvestments

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return investment, nil
}

func (s *service) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.Investment{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Investment, len(financialMap.Investments))
	for i := range financialMap.Investments {
		result[i] = &financialMap.Investments[i]
	}

	return result, nil
}

func (s *service) UpdateInvestment(ctx context.Context, id string, name string, currentValue monetary.Amount) error {
	_, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid investment ID", errors.Validation, err)
	}

	// This is a simplified implementation - in a real scenario, you'd need to identify the user
	// For now, we'll need to search across all financial maps or require userID
	return errors.New(errors.Service, "UpdateInvestment requires userID - use specialized update method", errors.NotImplemented, nil)
}

func (s *service) DeleteInvestment(ctx context.Context, id string) error {
	_, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid investment ID", errors.Validation, err)
	}

	// This is a simplified implementation - in a real scenario, you'd need to identify the user
	// For now, we'll need to search across all financial maps or require userID
	return errors.New(errors.Service, "DeleteInvestment requires userID - use specialized delete method", errors.NotImplemented, nil)
}

// Asset operations
func (s *service) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new asset
	asset := &dashboard.Asset{
		UserID:      userID,
		Description: description,
		Value:       value,
	}

	if err := asset.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.Assets = append(financialMap.Assets, *asset)

	// Recalculate total assets
	var totalAssets monetary.Amount
	for _, a := range financialMap.Assets {
		totalAssets += a.Value
	}
	financialMap.TotalAssets = totalAssets

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return asset, nil
}

func (s *service) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.Asset{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Asset, len(financialMap.Assets))
	for i := range financialMap.Assets {
		result[i] = &financialMap.Assets[i]
	}

	return result, nil
}

func (s *service) UpdateAsset(ctx context.Context, id string, description string, value monetary.Amount) error {
	_, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid asset ID", errors.Validation, err)
	}

	// This is a simplified implementation - in a real scenario, you'd need to identify the user
	// For now, we'll need to search across all financial maps or require userID
	return errors.New(errors.Service, "UpdateAsset requires userID - use specialized update method", errors.NotImplemented, nil)
}

func (s *service) DeleteAsset(ctx context.Context, id string) error {
	_, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid asset ID", errors.Validation, err)
	}

	// This is a simplified implementation - in a real scenario, you'd need to identify the user
	// For now, we'll need to search across all financial maps or require userID
	return errors.New(errors.Service, "DeleteAsset requires userID - use specialized delete method", errors.NotImplemented, nil)
}

// Snapshot operations
func (s *service) CreateMonthlySnapshot(ctx context.Context, userID string) error {
	// Get financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			// No financial map exists, nothing to snapshot
			return nil
		}
		return err
	}

	// Calculate current values
	var emergencyFundValue monetary.Amount
	if financialMap.EmergencyFund != nil {
		emergencyFundValue = financialMap.EmergencyFund.CurrentValue
	}

	// Create snapshot
	snapshot := dashboard.NetWorthSnapshot{
		UserID:             userID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         emergencyFundValue + financialMap.TotalInvestments + financialMap.TotalAssets,
	}

	if err := snapshot.PrepareCreate(); err != nil {
		return err
	}

	// Add snapshot to financial map history (limit to 12 months)
	financialMap.NetWorthHistory = append(financialMap.NetWorthHistory, snapshot)
	if len(financialMap.NetWorthHistory) > 12 {
		financialMap.NetWorthHistory = financialMap.NetWorthHistory[len(financialMap.NetWorthHistory)-12:]
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

func (s *service) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.NetWorthSnapshot{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Get the requested number of snapshots (most recent first)
	history := financialMap.NetWorthHistory
	if len(history) > limit {
		history = history[len(history)-limit:]
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.NetWorthSnapshot, len(history))
	for i := range history {
		result[i] = &history[i]
	}

	return result, nil
}
